<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use React\EventLoop\Loop;
use React\Socket\Connector;
use React\Stream\WritableResourceStream;
use Exception;

class OpenAIRealtimeService
{
    protected $apiKey;
    protected $websocketUrl;
    protected $loop;
    protected $connector;
    protected $connection;
    protected $isConnected = false;
    
    // Event callbacks
    protected $onMessage;
    protected $onError;
    protected $onConnect;
    protected $onDisconnect;

    public function __construct()
    {
        $this->apiKey = env('OPENAI_API_KEY');
        $this->websocketUrl = 'wss://api.openai.com/v1/realtime?model=gpt-4o-realtime-preview-2024-10-01';
        $this->loop = Loop::get();
        $this->connector = new Connector($this->loop);
    }

    /**
     * Connect to OpenAI Realtime API via WebSocket
     */
    public function connect(): \React\Promise\PromiseInterface
    {
        $headers = [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'OpenAI-Beta' => 'realtime=v1'
        ];

        $context = [
            'tls' => [
                'verify_peer' => true,
                'verify_peer_name' => true,
            ]
        ];

        Log::info('Attempting to connect to OpenAI Realtime API', [
            'url' => $this->websocketUrl
        ]);

        return $this->connector->connect($this->websocketUrl, $context)
            ->then(function ($connection) {
                $this->connection = $connection;
                $this->isConnected = true;
                
                Log::info('Successfully connected to OpenAI Realtime API');
                
                // Set up event listeners
                $this->setupEventListeners();
                
                // Send initial session configuration
                $this->sendSessionUpdate();
                
                if ($this->onConnect) {
                    call_user_func($this->onConnect);
                }
                
                return $connection;
            })
            ->otherwise(function ($error) {
                Log::error('Failed to connect to OpenAI Realtime API', [
                    'error' => $error->getMessage()
                ]);
                
                if ($this->onError) {
                    call_user_func($this->onError, $error);
                }
                
                throw $error;
            });
    }

    /**
     * Set up WebSocket event listeners
     */
    protected function setupEventListeners()
    {
        if (!$this->connection) {
            return;
        }

        // Handle incoming messages
        $this->connection->on('data', function ($data) {
            try {
                $message = json_decode($data, true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    Log::warning('Received invalid JSON from OpenAI Realtime API', [
                        'data' => $data
                    ]);
                    return;
                }
                
                Log::debug('Received message from OpenAI Realtime API', [
                    'type' => $message['type'] ?? 'unknown'
                ]);
                
                $this->handleIncomingMessage($message);
                
            } catch (Exception $e) {
                Log::error('Error processing incoming message', [
                    'error' => $e->getMessage(),
                    'data' => $data
                ]);
            }
        });

        // Handle connection close
        $this->connection->on('close', function () {
            $this->isConnected = false;
            Log::info('WebSocket connection closed');
            
            if ($this->onDisconnect) {
                call_user_func($this->onDisconnect);
            }
        });

        // Handle connection errors
        $this->connection->on('error', function ($error) {
            Log::error('WebSocket connection error', [
                'error' => $error->getMessage()
            ]);
            
            if ($this->onError) {
                call_user_func($this->onError, $error);
            }
        });
    }

    /**
     * Send session configuration to OpenAI
     */
    protected function sendSessionUpdate()
    {
        $sessionConfig = [
            'type' => 'session.update',
            'session' => [
                'modalities' => ['text', 'audio'],
                'instructions' => 'Sen yardımcı bir asistansın. Türkçe konuşuyorsun ve kullanıcılara yardım ediyorsun.',
                'voice' => 'alloy',
                'input_audio_format' => 'pcm16',
                'output_audio_format' => 'pcm16',
                'input_audio_transcription' => [
                    'model' => 'whisper-1'
                ],
                'turn_detection' => [
                    'type' => 'server_vad',
                    'threshold' => 0.5,
                    'prefix_padding_ms' => 300,
                    'silence_duration_ms' => 200
                ],
                'tools' => [],
                'tool_choice' => 'auto',
                'temperature' => 0.8,
                'max_response_output_tokens' => 'inf'
            ]
        ];

        $this->sendMessage($sessionConfig);
    }

    /**
     * Handle incoming messages from OpenAI
     */
    protected function handleIncomingMessage(array $message)
    {
        $type = $message['type'] ?? '';
        
        switch ($type) {
            case 'session.created':
                Log::info('Session created successfully');
                break;
                
            case 'session.updated':
                Log::info('Session updated successfully');
                break;
                
            case 'conversation.item.created':
                Log::debug('Conversation item created', ['item_id' => $message['item']['id'] ?? 'unknown']);
                break;
                
            case 'response.created':
                Log::debug('Response created', ['response_id' => $message['response']['id'] ?? 'unknown']);
                break;
                
            case 'response.done':
                Log::debug('Response completed');
                break;
                
            case 'response.audio.delta':
                // Handle audio streaming
                if (isset($message['delta'])) {
                    $this->handleAudioDelta($message['delta']);
                }
                break;
                
            case 'response.text.delta':
                // Handle text streaming
                if (isset($message['delta'])) {
                    $this->handleTextDelta($message['delta']);
                }
                break;
                
            case 'error':
                Log::error('OpenAI Realtime API error', [
                    'error' => $message['error'] ?? 'Unknown error'
                ]);
                break;
                
            default:
                Log::debug('Unhandled message type', ['type' => $type]);
        }
        
        // Call custom message handler if set
        if ($this->onMessage) {
            call_user_func($this->onMessage, $message);
        }
    }

    /**
     * Handle audio delta (streaming audio data)
     */
    protected function handleAudioDelta(string $audioDelta)
    {
        // Audio data is base64 encoded PCM16
        // You can decode and process it here
        Log::debug('Received audio delta', ['length' => strlen($audioDelta)]);
    }

    /**
     * Handle text delta (streaming text data)
     */
    protected function handleTextDelta(string $textDelta)
    {
        Log::debug('Received text delta', ['text' => $textDelta]);
    }

    /**
     * Send a message to OpenAI Realtime API
     */
    public function sendMessage(array $message): bool
    {
        if (!$this->isConnected || !$this->connection) {
            Log::warning('Cannot send message: not connected to OpenAI Realtime API');
            return false;
        }

        try {
            $json = json_encode($message);
            $this->connection->write($json);
            
            Log::debug('Sent message to OpenAI Realtime API', [
                'type' => $message['type'] ?? 'unknown'
            ]);
            
            return true;
        } catch (Exception $e) {
            Log::error('Failed to send message to OpenAI Realtime API', [
                'error' => $e->getMessage(),
                'message' => $message
            ]);
            return false;
        }
    }

    /**
     * Send text message to the conversation
     */
    public function sendTextMessage(string $text): bool
    {
        $message = [
            'type' => 'conversation.item.create',
            'item' => [
                'type' => 'message',
                'role' => 'user',
                'content' => [
                    [
                        'type' => 'input_text',
                        'text' => $text
                    ]
                ]
            ]
        ];

        if (!$this->sendMessage($message)) {
            return false;
        }

        // Trigger response generation
        $responseMessage = [
            'type' => 'response.create',
            'response' => [
                'modalities' => ['text', 'audio']
            ]
        ];

        return $this->sendMessage($responseMessage);
    }

    /**
     * Send audio data to the conversation
     */
    public function sendAudioData(string $audioBase64): bool
    {
        $message = [
            'type' => 'input_audio_buffer.append',
            'audio' => $audioBase64
        ];

        return $this->sendMessage($message);
    }

    /**
     * Commit audio buffer and trigger response
     */
    public function commitAudioBuffer(): bool
    {
        $message = [
            'type' => 'input_audio_buffer.commit'
        ];

        if (!$this->sendMessage($message)) {
            return false;
        }

        // Trigger response generation
        $responseMessage = [
            'type' => 'response.create',
            'response' => [
                'modalities' => ['text', 'audio']
            ]
        ];

        return $this->sendMessage($responseMessage);
    }

    /**
     * Set event callbacks
     */
    public function onMessage(callable $callback): self
    {
        $this->onMessage = $callback;
        return $this;
    }

    public function onError(callable $callback): self
    {
        $this->onError = $callback;
        return $this;
    }

    public function onConnect(callable $callback): self
    {
        $this->onConnect = $callback;
        return $this;
    }

    public function onDisconnect(callable $callback): self
    {
        $this->onDisconnect = $callback;
        return $this;
    }

    /**
     * Disconnect from OpenAI Realtime API
     */
    public function disconnect()
    {
        if ($this->connection) {
            $this->connection->close();
            $this->connection = null;
        }
        $this->isConnected = false;
    }

    /**
     * Check if connected
     */
    public function isConnected(): bool
    {
        return $this->isConnected;
    }

    /**
     * Run the event loop
     */
    public function run()
    {
        $this->loop->run();
    }

    /**
     * Stop the event loop
     */
    public function stop()
    {
        $this->loop->stop();
    }
}
